import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import FormRender, { useForm } from 'form-render';
import { history } from 'umi';
import { ColumnProps } from 'antd/es/table';
import { Card, Radio, Button, Table, Divider, Modal, message, Popover, Row, Col, Input, Form } from 'antd';
import { useGlobal } from '@ali/use-global';

import SearchUser from '@/components/UserSearch';
import CreateSelectModal from './CreateSelectModal';
import { getUrlParams } from '@/utils/utils';

import { formSchema } from './schema';
import { manageProps } from './interface';

import api from '@/api';

import './index.less';

const ManageCollect = (props: any) => {
  const urlParams = getUrlParams();
  const [global, setGlobal] = useGlobal();
  const [filterForm] = Form.useForm();

  const [state, setState] = useSet({
    auditStatus: '0', //0 我创建的 1 全部品集
    showModal: false, //弹窗显示
    askVisible: false, //删除弹窗
    askText: ['真的要删除吗', '删除行为不可逆，千万要想清楚啊'],
    delIndex: -1,
    delId: -1,
    loading: false,
    isOwner: true,
    pageSize: 10,
    pageNo: global?.pageNos || 1,
    total: 0,
    dataSource: [],
    query: null,
    createSelectModal: false, //选品集弹窗
    createSelectType: 'add', //add 新建选品集 edit 修改选品集
    createSelectFormData: {}, // 编辑信息
    filterParams: {
      id: '',
      title: ''
    }, // 筛选参数
  });
  const {
    auditStatus,
    showModal,
    loading,
    isOwner,
    pageSize,
    pageNo,
    total,
    askVisible,
    askText,
    dataSource,
    delId,
    createSelectModal,
    createSelectType,
    createSelectFormData,
    filterParams,
  } = state;

  // 监听路由全部品集进入查看选品集，返回显示
  useEffect(() => {
    let data: any;
    if (urlParams?.isOther) {
      data = {
        parentId: 0,
        pageNo: global?.pageNos || 1,
        pageSize,
        isOwner: false,
        isChild: 0,
        query: global?.query ? global.query : null,
        id: filterParams.id || undefined,
        title: filterParams.title || undefined,
      };
      setState({ auditStatus: '1', isOwner: false });
    } else {
      data = {
        parentId: 0,
        pageNo: global?.pageNos || 1,
        pageSize,
        isOwner: true,
        isChild: 0,
        query: global?.query ? global.query : null,
        id: filterParams.id || undefined,
        title: filterParams.title || undefined,
      };
      setState({ auditStatus: '0', isOwner: true });
    }
    setState({
      loading: true,
    });
    api.activity.list(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  }, [urlParams?.isOther, global?.query, isOwner, filterParams]);

  // 第一次请求
  const getData = (pageNo: any) => {
    const data = {
      parentId: 0,
      pageNo,
      pageSize,
      isOwner,
      isChild: 0,
      query: global?.query ? global.query : null,
      id: filterParams.id || undefined,
      title: filterParams.title || undefined,
    };
    setState({
      loading: true,
    });
    api.activity.list(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  };

  const form = useForm();

  const columns: ColumnProps<manageProps>[] = [
    {
      title: '选品集ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '选品集名称',
      dataIndex: 'activityTitle',
      key: 'activityTitle',
      align: 'center',
    },
    {
      title: '管理员',
      dataIndex: 'activityOwner',
      key: 'activityOwner',
      align: 'center',
      ellipsis: true,
      // width:100,
      render: (text) => <Popover content={text}>{text}</Popover>,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      align: 'center',
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 300,
      render: (text: any, record: any, index: number) => {
        return (
          <div>
            {record.childCount <= 0 && (
              <span>
                <Button type="link" onClick={() => activityDel(index, record.id)} disabled={!record.isOwner}>
                  删除
                </Button>
                <Divider type="vertical" />
              </span>
            )}
            <Button
              type="link"
              disabled={!record.isOwner}
              onClick={() => {
                setState({ createSelectModal: true, createSelectType: 'edit', createSelectFormData: record });
                // form.setValues({ ...record, activityOwner: record.activityOwner.split(',') });
              }}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              onClick={() => {
                history.push(
                  `/manageCollect/manageChild?activityTitle=${record.activityTitle}&id=${
                    record.id
                  }&isChild=${1}&isOwner=${isOwner}&pageNos=${pageNo}`,
                );
              }}
            >
              管理选品池
            </Button>
          </div>
        );
      },
    },
  ];

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    const data = {
      parentId: 0,
      pageNo: pageNum,
      pageSize,
      isOwner,
      isChild: 0,
      id: filterParams.id || undefined,
      title: filterParams.title || undefined,
    };
    setState({
      loading: true,
    });
    api.activity.list(data).then((res: any) => {
      if (res && res.data) {
        setGlobal({ pageNos: pageNum });
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  };

  const handleCancel = () => {
    setState({ showModal: false });
    form.resetFields();
  };

  const handleOk = () => {
    form.submit();
  };

  const onFinish = (formData: any, validation: any) => {
    if (validation.length) return;
    const data = {
      id: formData.id,
      activityTitle: formData.activityTitle,
      activityOwner: formData.activityOwner.toString(),
      activityIndex: 0,
    };
    api.activity.save(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        getData(pageNo);
      } else {
        message.error(res.msg);
      }
    });
    setState({ showModal: false });
  };

  // 删除
  const activityDel = (index: number, id: number) => {
    setState({
      delIndex: index,
      delId: id,
      askVisible: true,
    });
  };

  const askCancel = () => {
    setState({
      askVisible: false,
    });
  };
  const askOk = () => {
    setState({
      askVisible: false,
    });
    const data = {
      id: delId,
    };
    api.activity.delete(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        getData(pageNo);
      } else {
        message.error(res.msg);
      }
    });
  };

  // 关闭新建选品集弹窗
  const close = () => {
    setState({
      createSelectModal: false,
    });
  };

  // 筛选功能
  const onFilterSearch = () => {
    const filterValues = filterForm.getFieldsValue();
    setState({
      filterParams: {
        id: filterValues.id || '',
        title: filterValues.title || '',
      },
      pageNo: 1,
    });
    setGlobal({ pageNos: 1 });
  };

  const onFilterReset = () => {
    filterForm.resetFields();
    setState({
      filterParams: {
        id: '',
        title: '',
      },
      pageNo: 1,
    });
    setGlobal({ pageNos: 1 });
  };

  // 验证ID只能输入纯数字
  const validateId = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    if (!/^\d+$/.test(value)) {
      return Promise.reject(new Error('请输入纯数字'));
    }
    return Promise.resolve();
  };

  return (
    <>
      <Modal title="编辑活动" visible={showModal} width={740} onOk={handleOk} onCancel={handleCancel}>
        <FormRender
          form={form}
          schema={formSchema}
          onFinish={onFinish}
          displayType="row"
          labelWidth={120}
          widgets={{
            searchUser: SearchUser,
          }}
        />
      </Modal>
      <Modal
        visible={askVisible}
        onCancel={askCancel}
        title={askText[0]}
        footer={[
          <Button key="back" size="large" onClick={askCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" size="large" onClick={askOk}>
            确定
          </Button>,
        ]}
      >
        {askText[1]}
      </Modal>
      <CreateSelectModal
        createSelectModal={createSelectModal}
        close={close}
        getData={getData}
        createSelectType={createSelectType}
        createSelectFormData={createSelectFormData}
        pageNo={pageNo}
      />
      
      {/* 搜索筛选区域 */}
      <Card className="filter-card" size="small">
        <Form form={filterForm}>
          <div className="filter-form">
            <div className="filter-form-content">
              <div className="filter-form-row">
                <Form.Item 
                  label="选品集ID" 
                  name="id" 
                  rules={[{ validator: validateId }]} 
                  className="filter-form-item"
                >
                  <Input placeholder="请输入选品集ID" />
                </Form.Item>
                <Form.Item 
                  label="选品集名称" 
                  name="title" 
                  className="filter-form-item"
                >
                  <Input placeholder="请输入选品集名称" />
                </Form.Item>
              </div>
            </div>
            <div className="filter-buttons">
              <Button type="primary" onClick={onFilterSearch}>
                搜索
              </Button>
              <Button onClick={onFilterReset}>
                重置
              </Button>
            </div>
          </div>
        </Form>
      </Card>

      {/* 选品集管理区域 */}
      <Card 
        className="manage-main"
      >
        <div className="manage-main-top">
          <Radio.Group
            value={auditStatus}
            onChange={(e) => {
              setState({ auditStatus: e.target.value });
              setGlobal({ query: null, pageNos: undefined });
              e.target.value === '0' ? setState({ isOwner: true }) : setState({ isOwner: false });
              e.target.value === '0' ? history.push(`/manageCollect`) : history.push(`/manageCollect?isOther=${true}`);
            }}
          >
            <Radio.Button value="0">我创建的</Radio.Button>
            <Radio.Button value="1">全部品集</Radio.Button>
          </Radio.Group>
          <Button
            type="primary"
            onClick={() => {
              setState({
                createSelectModal: true,
                createSelectType: 'add',
              });
            }}
          >
            新建选品集
          </Button>
        </div>

        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{ x: 1300 }}
          pagination={{
            pageSize: pageSize,
            current: pageNo,
            total: total,
            onChange: (num, pageSize) => onPaginationChange(num, pageSize),
          }}
        />
      </Card>
    </>
  );
};

export default ManageCollect;
