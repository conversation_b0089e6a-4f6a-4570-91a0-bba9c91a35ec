import React, { useState, useEffect } from 'react';
import { useSet } from '@/utils/hooks';
import { useForm } from 'form-render';
import { history } from 'umi';
import { ColumnProps } from 'antd/es/table';
import {
  Card,
  Breadcrumb,
  Button,
  Table,
  Divider,
  Modal,
  Row,
  Col,
  Tag,
  DatePicker,
  message,
  Menu,
  Dropdown,
  Popover,
  Form,
  Input,
  Select,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { useGlobal } from '@ali/use-global';
import { manageChildProps } from './interface';
import { get, cloneDeep } from 'lodash';
import moment from 'moment';
import { getUrlParams, poolStatus } from '@/utils/utils';
import ModalRecord from '@/components/ModalRecord';
import BatchExportModal from './BatchExportModal';
import api from '@/api';
import styles from './index.less';

const { Option } = Select;

const ManageChild = (props: any) => {
  const urlParams = getUrlParams();
  const [global, setGlobal] = useGlobal();
  const [filterForm] = Form.useForm();
  
  const [state, setState] = useSet({
    editModal: false, //编辑弹窗
    batchExpireTimeVisible: false, //批量延期弹窗
    batchExportVisible: false, //批量导出
    loading: false,
    selectedRowKeys: [],
    dataSource: [],
    pageSize: 10,
    pageNo: 1,
    total: 0,
    expireTime: '',
    askVisible: false,
    status: '',
    childId: null,
    delIndex: 0,
    askText: [],
    dataTypeList: {}, //货品类型
    allText: {
      del: ['真的要删除吗', '删除行为不可逆，千万要想清楚啊'],
      open: (time: any) => [
        '真的要启用吗',
        <div className={styles.askTextContent}>
          <span className={styles.askTextBlock}>启用的后果：</span>
          <span className={styles.askTextIndent}>①立即重新发布本商品池（所以会立即开始打标）</span>
          <span className={styles.askTextIndent}>②下游将会拿到本商品池数据</span>
          <span className={styles.askTextIndent}>③若设置了每天定时发布，将会重新被执行 </span>
          <span className={styles.askTextIndentBottom}>
            ④需要等启用任务完成后，才可重新禁用
          </span>

          <span className={styles.askTextError}>当前活动过期时间为：{time}</span>
          <span className={styles.askTextBlock2}>
            如果需要<b className={styles.askTextBold}>修改过期时间</b>，启用后点击
            <span className={styles.askTextLink}>编辑</span>重新设置
          </span>
        </div>,
      ],
      off: [
        '真的要禁用吗',
        '禁用的后果：①立即清标 ②下游可能拿不到本商品池的数据了 ③每天的定时发布不再执行了 ④需要等禁用任务完成后，才可重新启用',
      ],
    },
    isRecordVisible: false, //历史记录弹窗
    ids: null, //历史纪录id
    parentIsOwner: false,
    filterParams: {
      id: '',
      title: '',
      dataSource: '',
      status: ''
    }, // 筛选参数
  });

  const {
    batchExpireTimeVisible,
    batchExportVisible,
    loading,
    pageSize,
    pageNo,
    total,
    selectedRowKeys,
    expireTime,
    dataSource,
    askVisible,
    askText,
    dataTypeList,
    isRecordVisible,
    ids,
    activityOwner,
    filterParams,
  } = state;

  const form = useForm();

  const { location } = props;

  const { activityTitle, id, isChild, isOwner, isSearch, pageNos } = location.query;

  useEffect(() => {
    getData();
  }, [id, global?.query, isSearch, filterParams]);

  useEffect(() => {
    api.rule.dataSource().then((res: any) => {
      if (res.code === '200') {
        setState({
          dataTypeList: res.data,
        });
      } else {
        message.error(res.msg);
      }
    });

    api.activity.getActivity({ id, fetch: false }).then((res: any) => {
      if (res.code === '200' && res.data && res.data.activity) {
        setState({
          activityOwner: res.data.activity?.activityOwner,
        });
      } else {
        message.error(res.msg);
      }
    });
  }, []);

  // 第一次请求
  const getData = () => {
    let data: any = {};
    if (isSearch) {
      data = {
        pageNo,
        pageSize,
        isChild,
        query: global?.query ? global.query : null,
        id: filterParams.id || undefined,
        title: filterParams.title || undefined,
        dataSource: filterParams.dataSource || undefined,
        status: filterParams.status || undefined,
      };
      setState({
        loading: true,
      });
      api.activity.list(data).then((res: any) => {
        if (res && res.data) {
          setState({
            loading: false,
            dataSource: res.data,
            pageNo: res.pageNo,
            pageSize: res.pageSize,
            total: res.total,
          });
        }
      });
    } else {
      data = {
        parentId: id,
        pageNo,
        pageSize,
        isOwner,
        isChild,
        id: filterParams.id || undefined,
        title: filterParams.title || undefined,
        dataSource: filterParams.dataSource || undefined,
        status: filterParams.status || undefined,
      };
      setState({
        loading: true,
      });
      api.activity.list(data).then((res: any) => {
        if (res && res.data) {
          setState({
            loading: false,
            dataSource: res.data,
            pageNo: res.pageNo,
            pageSize: res.pageSize,
            total: res.total,
          });
        }
      });
    }
  };

  const columns: ColumnProps<manageChildProps>[] = [
    {
      title: '品池ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '品池名称',
      dataIndex: 'activityTitle',
      key: 'activityTitle',
      align: 'center',
    },
    {
      title: '物料类型',
      dataIndex: 'dataSource',
      key: 'dataSource',
      align: 'center',
      render: (text: any, record: any, index: number) => {
        let dataType;
        if (dataTypeList && Object.getOwnPropertyNames(dataTypeList).length > 0) {
          Object.keys(dataTypeList).map((item: any) => {
            if (text.toUpperCase() === dataTypeList[item]) {
              dataType = item;
            }
          });
        }
        return dataType;
      },
    },
    {
      title: '创建类型',
      dataIndex: 'createType',
      key: 'createType',
      align: 'center',
      render: (text: number) => {
        if (text === 0) {
          return '自由选品';
        }
        if (text === 1) {
          return '上传品集';
        }
        if (text === 2) {
          return '盘点品集';
        }
        return '';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text, record, index) => {
        const statusInfo = poolStatus(record?.status);
        const statusTag = <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
        
        // 如果是发布失败状态且有错误信息，显示Popover提示
        if (record?.status === 5 && record?.extInfo?.buildErrorMsg) {
          return (
            <Popover 
              content={
                <div className={styles.popoverContent}>
                  <div>{record.extInfo.buildErrorMsg}</div>
                </div>
              }
              title="失败原因"
              trigger="hover"
              placement="topLeft"
            >
              {statusTag}
            </Popover>
          );
        }
        
        return statusTag;
      },
    },
    {
      title: '归属人',
      dataIndex: 'activityOwner',
      key: 'activityOwner',
      align: 'center',
      ellipsis: true,
      render: (text) => <Popover content={text}>{text}</Popover>,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      align: 'center',
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      align: 'center',
    },
    {
      title: '过期时间',
      dataIndex: 'expireTime',
      key: 'expireTime',
      align: 'center',
    },
    {
      title: '打标任务',
      dataIndex: 'hasTag',
      key: 'hasTag',
      align: 'center',
      render: (text, record, index) => {
        if (text - 0 === 1) {
          return <span>有</span>;
        } else {
          return <span>无</span>;
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'handle',
      key: 'handle',
      align: 'center',
      fixed: 'right',
      width: 380,
      render: (text, record, index) => onOperationChange(text, record, index),
    },
  ];

  // 切换页码
  const onPaginationChange = (pageNum: number, pageSize: any) => {
    const data = {
      parentId: id,
      pageNo: pageNum,
      pageSize,
      isOwner,
      isChild,
      id: filterParams.id || undefined,
      title: filterParams.title || undefined,
      dataSource: filterParams.dataSource || undefined,
      status: filterParams.status || undefined,
    };
    setState({
      loading: true,
    });
    api.activity.list(data).then((res: any) => {
      if (res && res.data) {
        setState({
          loading: false,
          dataSource: res.data,
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.total,
        });
      }
    });
  };

  // 权限控制
  const isPower = () => {
    let temp: any[] = [];
    activityOwner &&
      activityOwner.split(',').forEach((ele: any) => {
        if (ele.match(/\[(.+?)\]/g)) {
          ele.match(/\[(.+?)\]/g);
          temp.push(RegExp.$1);
        } else {
          temp.push(ele);
        }
      });
    if (temp.includes(get(global, 'createOwner.empId', '')) || temp.includes(get(global, 'createOwner.nick', ''))) {
      return true;
    }
    return false;
  };

  // 操作选品逻辑
  const selectionChange = (status: any, record: any) => {
    if (!record.isOwner || status === 2) {
      return <span>选品</span>;
    }

    if (record.isOwner && status === 4) {
      return (
        <Button
          href={
            window.location.href.includes('isFrame=true')
              ? `https://${window.location.host}/#/createCollect?type=${
                  Number(record.createType) + 1
                }&step=4&collectionId=${record.id}&isFrame=true&activityId=${id}&activityTitle=${activityTitle}`
              : `https://${window.location.host}/#/createCollect?type=${
                  Number(record.createType) + 1
                }&step=4&collectionId=${record.id}`
          }
          type="link"
        >
          选品
        </Button>
      );
    }

    return (
      <Button
        href={
          window.location.href.includes('isFrame=true')
            ? `https://${window.location.host}/#/createCollect?type=${
                Number(record.createType) + 1
              }&step=2&collectionId=${
                record.id
              }&isFrame=true&activityId=${id}&activityTitle=${activityTitle}&isEdit=true`
            : `https://${window.location.host}/#/createCollect?type=${
                Number(record.createType) + 1
              }&step=2&collectionId=${record.id}&isEdit=true`
        }
        type="link"
      >
        选品
      </Button>
    );
  };

  // 操作
  const onOperationChange = (text: any, record: any, index: number) => {
    const menu = (
      <Menu>
        <Menu.Item
          key="1"
          onClick={() => {
            exportItem(record);
          }}
        >
          导出excel
        </Menu.Item>
        <Menu.Item
          key="2"
          onClick={() => {
            window.open('https://yuque.antfin.com/docs/share/b2d44934-1c79-413d-b22f-49e121dd533d');
          }}
        >
          导出odps
        </Menu.Item>
      </Menu>
    );

    // 操作更多
    const moreMenu = (record: any) => (
      <Menu>
        {record.status - 0 === 2 ? (
          <Menu.Item key="1">
            <Button type="link" onClick={() => statusFn('del', record.id, index)}>
              删除
            </Button>
          </Menu.Item>
        ) : null}
        <Menu.Item key="2">
          {record.status - 0 != 2 ? (
            <Button
              type="link"
              disabled={record.uploadType === 'ODPS'}
              onClick={() => {
                onCopy(record);
              }}
            >
              复制
            </Button>
          ) : (
            <Button disabled={record.uploadType === 'ODPS'} type="link" style={{ color: '#000' }}>
              复制
            </Button>
          )}
        </Menu.Item>
        <Menu.Item key="3">
          {!record.isOwner ||
          record.hasTag === 1 ||
          record.createType === 1 ||
          record.createType === 2 ||
          record.status - 0 !== 1 ? (
            <Button type="link" style={{ color: '#000' }}>
              重跑
            </Button>
          ) : (
            <Button
              type="link"
              onClick={() => {
                onRerun(record);
              }}
            >
              重跑
            </Button>
          )}
        </Menu.Item>
        {record.status - 0 === 2 ? (
          <Menu.Item key="4">
            <Button type="link" onClick={() => statusFn('open', record.id, index)}>
              启用
            </Button>
          </Menu.Item>
        ) : null}
        {record.status - 0 === 4 ? (
          <Menu.Item key="5">
            <Button type="link" style={{ color: '#000' }}>
              禁用
            </Button>
          </Menu.Item>
        ) : null}
        {record.isOwner && record.status - 0 !== 4 && record.status - 0 !== 2 ? (
          <Menu.Item key="5">
            <Button type="link" onClick={() => statusFn('off', record.id, index)}>
              禁用
            </Button>
          </Menu.Item>
        ) : null}
        <Menu.Item key="6">
          <Button
            type="link"
            onClick={() => {
              openRecord(record);
            }}
          >
            操作历史
          </Button>
        </Menu.Item>
        {/* <Menu.Item key="7">
          <Button
            type="link"
            onClick={() => {
              history.push(
                `/tool/filterquery?activityId=${record.id}`,
              )
            }}
          >
            问题排查
          </Button>
        </Menu.Item> */}
      </Menu>
    );

    return (
      <span className={styles.manageOperation}>
        {record.isOwner && record.status - 0 != 2 ? (
          <Button
            type="link"
            onClick={() => {
              if (window.location.href.includes('isFrame=true')) {
                history.push(
                  `/manageChildEdit?id=${record.id}&parentId=${id}&activityParentTitle=${activityTitle}&activityChildTitle=${record.activityTitle}&isOwner=${isOwner}&isFrame=true`,
                );
              } else {
                history.push(
                  `/manageChildEdit?id=${record.id}&parentId=${id}&activityParentTitle=${activityTitle}&activityChildTitle=${record.activityTitle}&isOwner=${isOwner}`,
                );
              }
            }}
          >
            编辑
          </Button>
        ) : (
          <span>编辑</span>
        )}
        <Divider type="vertical" />
        {selectionChange(record.status - 0, record)}
        <Divider type="vertical" />
        {record.status - 0 === 1 ? (
          <Button
            href={
              window.location.href.includes('isFrame=true')
                ? `https://${window.location.host}/#/createCollect/goodsPool?collectionId=${record.id}&isFrame=true&activityId=${id}&activityTitle=${activityTitle}`
                : `https://${window.location.host}/#/createCollect/goodsPool?collectionId=${record.id}`
            }
            type="link"
          >
            线上商品池
          </Button>
        ) : (
          <span>线上商品池</span>
        )}
        <Divider type="vertical" />
        {record.isOwner && record.status - 0 === 1 ? (
          <Dropdown overlay={menu}>
            <Button type="link">导出</Button>
          </Dropdown>
        ) : (
          <span>导出</span>
        )}
        <Divider type="vertical" />
        <Button
          type="link"
          onClick={() => {
            if (window.location.href.includes('isFrame=true')) {
              history.push(`/tool/filterquery?activityId=${record.id}&isFrame=true`);
            } else {
              history.push(`/tool/filterquery?activityId=${record.id}`);
            }
          }}
        >
          问题排查
        </Button>
        <Divider type="vertical" />
        <Dropdown overlay={moreMenu(record)} placement="bottomCenter" trigger={['hover']}>
          <a>更多</a>
        </Dropdown>
      </span>
    );

    // 老逻辑不需要了，先注释
    // if (record.isOwner) {
    //   return (
    //     <span className={styles.manageOperation}>
    //       {isPower(record) && record.status - 0 != 2 ? (
    //         <Button
    //           type="link"
    //           onClick={() => {
    //             history.push(
    //               `/manageChildEdit?id=${record.id}&parentId=${id}&activityParentTitle=${activityTitle}&activityChildTitle=${record.activityTitle}&isOwner=${isOwner}`,
    //             );
    //           }}
    //         >
    //           编辑
    //         </Button>
    //       ) : (
    //         <span>编辑</span>
    //       )}
    //       <Divider type="vertical" />
    //       {selectionChange(record.status - 0, record)}
    //       <Divider type="vertical" />
    //       {record.status - 0 === 1 ? (
    //         <Button
    //           href={`https://${window.location.host}/#/createCollect/goodsPool?collectionId=${record.id}`}
    //           type="link"
    //         >
    //           线上商品池
    //         </Button>
    //       ) : (
    //         <span>线上商品池</span>
    //       )}
    //       <Divider type="vertical" />
    //       {isPower(record) && record.status - 0 === 1 ? (
    //         <Dropdown overlay={menu}>
    //           <Button type="link">导出</Button>
    //         </Dropdown>
    //       ) : (
    //         <span>导出</span>
    //       )}
    //       <Divider type="vertical" />
    //       <Button
    //         type="link"
    //         onClick={() => {
    //           history.push(
    //             `/tool/filterquery?activityId=${record.id}`,
    //           )
    //         }}
    //       >
    //         问题排查
    //       </Button>
    //       <Divider type="vertical" />
    //       <Dropdown overlay={moreMenu(record)} placement="bottomCenter" trigger={['hover']}>
    //         <a>更多</a>
    //       </Dropdown>
    //     </span>
    //   );
    // } else {
    //   return <span>仅子活动归属人可访问</span>;
    // }
  };

  // 禁用、启用、删除
  const statusFn = (text: any, id: number, index: number) => {
    const { askVisible, askText, allText } = state;
    setState({
      askVisible: true,
      askText:
        typeof allText[text] === 'function'
          ? allText[text](get(dataSource, `[${index}].expireTime`, '未获取到过期时间'))
          : allText[text],
      status: text,
      childId: id,
      delIndex: index,
    });
  };

  const askCancel = () => {
    setState({
      askVisible: false,
    });
  };

  const askOk = () => {
    const { delIndex, dispatch, status, childId } = state;
    setState({
      askVisible: false,
    });

    if (status === 'off') {
      const newData = cloneDeep(dataSource);
      newData[delIndex].status = 2;
      setState({
        dataSource: newData,
      });
      const data = {
        id: childId,
      };
      api.activity.stop(data).then((res: any) => {
        if (res.code === '200') {
          message.success(res.msg);
        } else {
          message.error(res.msg);
        }
      });
    } else if (status === 'open') {
      const data = {
        id: childId,
      };
      api.activity.start(data).then((res: any) => {
        if (res.code === '200') {
          message.success(res.msg);
          getData();
        } else {
          message.error(res.msg);
        }
      });
    } else if (status === 'del') {
      const data = {
        id: childId,
      };
      api.activity.delete(data).then((res: any) => {
        if (res.code === '200') {
          message.success(res.msg);
          getData();
        } else {
          message.error(res.msg);
        }
      });
    }
  };

  // 批量延期

  const BatchDelayActivity = () => {
    const data = {
      idList: selectedRowKeys && selectedRowKeys.toString(),
      expireTime: expireTime && moment(expireTime).format('YYYY-MM-DD HH:mm:ss'),
    };
    api.activity.batchDelayActivity(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        setState({
          batchExpireTimeVisible: false,
          selectedRowKeys: [],
        });
        getData();
      } else {
        message.error(res.msg);
      }
    });
  };

  // 复制
  const onCopy = (record: any) => {
    const data = {
      id: record.id,
    };
    api.activity.copy(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        getData();
      } else {
        message.error(res.msg);
      }
    });
  };

  // 导出
  const exportItem = (record: any) => {
    const data = {
      activityIds: record.id,
      itemCount: record.itemCount ? record.itemCount : undefined,
      type: 'single',
    };

    api.activity.exportItem(data).then((res: any) => {
      if (res.code === '200' && res.data) {
        message.success(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  // 重跑
  const onRerun = (record: any) => {
    const data = {
      id: record.id,
    };
    api.activity.OneClickPublish(data).then((res: any) => {
      if (res.code === '200') {
        message.success(res.msg);
        getData();
      } else {
        message.error(res.msg);
      }
    });
  };

  // 打开历史纪录
  const openRecord = (record: any) => {
    setState({
      isRecordVisible: true,
      ids: record.id,
    });
  };

  // 关闭历史纪录
  const closeRecord = () => {
    setState({
      isRecordVisible: false,
      ids: null,
    });
  };

  const close = () => {
    setState({
      batchExportVisible: false,
    });
  };

  // 筛选功能
  const onFilterSearch = () => {
    const filterValues = filterForm.getFieldsValue();
    setState({
      filterParams: {
        id: filterValues.id || '',
        title: filterValues.title || '',
        dataSource: filterValues.dataSource || '',
        status: filterValues.status || '',
      },
      pageNo: 1,
    });
  };

  const onFilterReset = () => {
    filterForm.resetFields();
    setState({
      filterParams: {
        id: '',
        title: '',
        dataSource: '',
        status: '',
      },
      pageNo: 1,
    });
  };

  // 验证ID只能输入纯数字
  const validateId = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    if (!/^\d+$/.test(value)) {
      return Promise.reject(new Error('请输入纯数字'));
    }
    return Promise.resolve();
  };

  return (
    <>
      {/* 搜索筛选区域 */}
      <Card className={`${styles.manageChildMain} ${styles.filterCard}`} size="small">
        <Form form={filterForm}>
          <div className={styles.filterForm}>
            <div className={styles.filterFormContent}>
              <div className={styles.filterFormRow}>
                <Form.Item 
                  label="选品池ID" 
                  name="id" 
                  rules={[{ validator: validateId }]} 
                  className={styles.filterFormItem}
                >
                  <Input placeholder="请输入选品池ID" />
                </Form.Item>
                <Form.Item 
                  label="选品池名称" 
                  name="title" 
                  className={styles.filterFormItem}
                >
                  <Input placeholder="请输入选品池名称" />
                </Form.Item>
              </div>
              <div className={styles.filterFormRow}>
                <Form.Item 
                  label="物料类型" 
                  name="dataSource" 
                  className={styles.filterFormItem}
                >
                  <Select placeholder="请选择物料类型" allowClear>
                    <Option value="TRAVEL">淘系宝贝</Option>
                    <Option value="POI">POI</Option>
                    <Option value="HOTEL">日历房</Option>
                    <Option value="SHOP">商家</Option>
                    <Option value="BNB">民宿</Option>
                    <Option value="GLB">哥伦布榜单</Option>
                    <Option value="SPU">SPU</Option>
                  </Select>
                </Form.Item>
                <Form.Item 
                  label="选品池状态" 
                  name="status" 
                  className={styles.filterFormItem}
                >
                  <Select placeholder="请选择状态" allowClear>
                    <Option value="3">待发布</Option>
                    <Option value="4">发布中</Option>
                    <Option value="1">发布成功</Option>
                    <Option value="5">发布失败</Option>
                    <Option value="2">停用</Option>
                  </Select>
                </Form.Item>
              </div>
            </div>
            <div className={styles.filterButtons}>
              <Button type="primary" onClick={onFilterSearch}>
                搜索
              </Button>
              <Button onClick={onFilterReset}>
                重置
              </Button>
            </div>
          </div>
        </Form>
      </Card>

      {/* 选品池列表区域 */}
      <Card>
        <div className={`${styles.manageChildTop} ${styles.listHeader}`}>
          <Breadcrumb separator=">">
            {!window.location.href.includes('isFrame=true') && (
              <Breadcrumb.Item>
                <a
                  className={styles.manageChildBreadcrumb}
                  style={{
                    color: '#1890ff',
                    cursor: 'pointer',
                    textDecoration: 'none'
                  }}
                  onClick={() => {
                    setGlobal({ query: null });
                    isOwner === 'true'
                      ? history.push(`/manageCollect`)
                      : history.push(`/manageCollect?isOther=${true}`);
                  }}
                  onMouseEnter={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    (e.target as HTMLAnchorElement).style.color = '#40a9ff';
                  }}
                  onMouseLeave={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    (e.target as HTMLAnchorElement).style.color = '#1890ff';
                  }}
                >
                  选品集列表
                </a>
              </Breadcrumb.Item>
            )}
            {!isSearch ? <Breadcrumb.Item>{activityTitle}</Breadcrumb.Item> : null}
          </Breadcrumb>
          
          <div className={styles.headerButtons}>
          <Button
            className={styles.manageChildBtnleft}
            type="primary"
            onClick={() => {
              if (isPower()) {
                if (window.location.href.includes('isFrame=true')) {
                  window.open(
                    `https://${window.location.host}/#/createCollect?type=1&step=1&activityId=${id}&isFrame=true&activityTitle=${activityTitle}`,
                    '_self',
                  );
                } else {
                  window.open(
                    `https://${window.location.host}/#/createCollect?type=1&step=1&activityId=${id}`,
                    '_self',
                  );
                }
              } else {
                if (window.location.href.includes('isFrame=true')) {
                  window.open(
                    `https://${window.location.host}/#/createCollect?type=1&step=1&activityId=${id}&isFrame=true&activityTitle=${activityTitle}`,
                    '_self',
                  );
                } else {
                  Modal.error({
                    title: '暂无权限在当前选品集下创建选品池',
                    content: '可联系选品集管理员添加权限',
                  });
                }
              }
            }}
          >
            新建选品池
          </Button>
          <Button className={styles.manageChildBtnleft} onClick={() => setState({ batchExpireTimeVisible: true })}>
            批量延期
          </Button>
          <Button
            className={styles.manageChildBtnleft}
            onClick={() => setState({ batchExportVisible: true })}
            disabled={!isPower()}
          >
            批量导出
          </Button>
          </div>
        </div>

        <Table
          rowSelection={{
            selectedRowKeys,
            preserveSelectedRowKeys: true,
            onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
              setState({ selectedRowKeys: selectedRowKeys });
            },
          }}
          scroll={{ x: 1600 }}
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={{
            pageSize: pageSize,
            current: pageNo,
            total: total,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100', '500'],
            showTotal: (total) => `总计${total}条`,
            onChange: (num, pageSize) => onPaginationChange(num, pageSize),
          }}
        />
      </Card>
      <Modal
        visible={batchExpireTimeVisible}
        title="批量延期"
        onCancel={() => setState({ batchExpireTimeVisible: false })}
        onOk={() => {
          BatchDelayActivity();
        }}
      >
        <Row className={styles.batchModalContent}>
          <Col className={styles.batchModalTitle}>已选活动ID：</Col>
          <Col className={styles.batchModalTags}>
            {selectedRowKeys && selectedRowKeys.length ? (
              selectedRowKeys.map((item: any) => <Tag color="orange">{item}</Tag>)
            ) : (
              <div className={styles.batchModalNoData}>暂无选择的活动</div>
            )}
          </Col>
          <div className={styles.batchModalExpire}>
            <Col span="5" className={styles.batchModalExpireLabel}>
              <b className={styles.batchModalRequired}>*</b> 过期时间：
            </Col>
            <Col span="19">
              <DatePicker
                value={expireTime}
                onChange={(values) => {
                  setState({ expireTime: values });
                }}
                className={styles.batchModalExpireInput}
              />
            </Col>
          </div>
        </Row>
      </Modal>
      <Modal
        visible={askVisible}
        onCancel={askCancel}
        title={askText[0]}
        footer={[
          <Button key="back" size="large" onClick={askCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" size="large" onClick={askOk}>
            确定
          </Button>,
        ]}
      >
        {askText[1]}
      </Modal>
      {/* 批量导出 */}
      <BatchExportModal
        batchExportVisible={batchExportVisible}
        dataSource={dataSource}
        selectedRowKeys={selectedRowKeys}
        onReload={getData}
        close={close}
      />
      {isRecordVisible ? <ModalRecord isRecordVisible={isRecordVisible} closeRecord={closeRecord} ids={ids} /> : null}
    </>
  );
};

export default ManageChild;
