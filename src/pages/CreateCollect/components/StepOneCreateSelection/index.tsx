import React, { useState, useEffect } from 'react';
import { Button, Radio, Input, Select, DatePicker, message, Form } from 'antd';
import moment from 'moment';
import { get } from 'lodash';

import SearchUser from '@/components/UserCollectSearch';

import { updateHash, getUrlParams } from '@/utils/utils';
import { FilterType, UpdateTimeTypeEnum, CollectStatusEnum, ApplyScenesEnum, APPLY_SCENES_MAP } from '../../constants';
import { useGlobal } from '@ali/use-global';
import api from '@/api';

import styles from './index.less';

const { RangePicker } = DatePicker;

const { TextArea } = Input;

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const StepOneCreateSelection = () => {
  const [form] = Form.useForm();
  const urlParams = getUrlParams();
  const [global, setGlobal] = useGlobal();

  const [goodTypeOptions, setGoodTypeOptions] = useState<{ label: string; value: number }[]>([]);
  const [goodsTypeValue, setGoodsTypeValue] = useState<any>('');
  const [createOwner, setCreateOwner] = useState<any>('');

  useEffect(() => {
    api.activity.dataSourceList().then((res) => {
      const temp = Object.keys(res)
        .filter((key) => res[key] !== 'RP' && res[key] !== 'FLIGHT' && res[key] !== 'LP') // 过滤掉酒店日历房RP选项、机票、LP页面
        .map((e) => {
          return {
            label: e,
            value: res[e],
          };
        });
      setGoodTypeOptions(temp);
    });
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      collectionOwner:
        get(global, 'createOwner.nick', '') && get(global, 'createOwner.empId', '')
          ? [`${get(global, 'createOwner.nick', '')}[${get(global, 'createOwner.empId', '')}]`]
          : [get(global, 'createOwner.nick', '')],
    });
  }, [global?.createOwner]);

  const jumpToNextStep = (collectionId: string) => {
    // 跳转到下一步
    updateHash({
      params: {
        type: urlParams.type, // 选品类型FilterType
        step: CollectStatusEnum.SETCOLLECTCONDITION,
        collectionId, // 创建成功的品集id
        isFrame: window.location.href.includes('isFrame=true') ? true : false,
        isEdit: true,
      },
    });
  };

  const createFinish = (formData: any) => {
    const temp = {
      activityTitle: formData.collectionName, // 品集名称
      expireTime: moment(formData.expireTime).format('YYYY-MM-DD HH:mm:ss'),
      parentId: urlParams?.activityId, // 主活动id,这个需要备注一下
      activityOwner: formData.collectionOwner.join(','),
      dataSource: formData.goodsType, // 货品类型
      hasTag: formData.hasTag ? 1 : 0, // 转成0，1
      updateType: formData.updateTime, // 更新时间
      createType: Number(formData.createType) - 1,
      applyScenes: formData.applyScenes,
    };

    if (formData?.subTitle) {
      temp.subTitle = formData?.subTitle;
    }

    api.activity.save(temp).then((res: any) => {
      if (res.code === '200' && res.msg === '操作成功') {
        message.success('品集创建成功！');
        jumpToNextStep(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };

  const onSelectType = (e: any) => {
    updateHash({
      params: {
        type: e.target.value, // 选品类型FilterType
      },
    });
  };

  // 时间过期
  const disabledDate = (current: any) => {
    return current < moment().startOf('day') || current > moment() + 90 * 24 * 60 * 60 * 1000;
  };

  // 切换物料类型
  const onGoodsType = (e: any) => {
    setGoodsTypeValue(e.target.value);
    if (e.target.value === 'SPU') {
      form.setFieldValue('createType', FilterType.Upload);
      updateHash({
        params: {
          type: FilterType.Upload, // 选品类型FilterType
        },
      });
    } else {
      form.setFieldValue('createType', FilterType.FreeSelect);
      updateHash({
        params: {
          type: FilterType.FreeSelect, // 选品类型FilterType
        },
      });
    }
  };

  return (
    <div className={styles.createForm}>
      <div className={styles.formContainer}>
        <Form {...layout} form={form} onFinish={createFinish}>
          <Form.Item label="选品池名称" name="collectionName" rules={[{ required: true, message: '请输入选品池名称' }]}>
            <Input placeholder="请输入选品池名称" />
          </Form.Item>
          <Form.Item
            label="选品人员"
            name="collectionOwner"
            rules={[{ required: true, message: '请输入选品人员' }]}
            extra={<span style={{ color: '#999' }}>不在列表中的人员仅可查看，不能变更选品规则</span>}
          >
            <SearchUser mode="multiple" />
          </Form.Item>
          <Form.Item label="过期时间" name="expireTime" rules={[{ required: true, message: '请选择生效时间' }]}>
            <DatePicker showTime disabledDate={disabledDate} />
          </Form.Item>

          <Form.Item
            label="物料类型"
            name="goodsType"
            rules={[{ required: true, message: '请选择物料类型' }]}
            initialValue="TRAVEL"
          >
            <Radio.Group
              options={goodTypeOptions}
              className={styles.goodsTypeRadio}
              onChange={onGoodsType}
            ></Radio.Group>
          </Form.Item>

          <Form.Item
            label="选品方式"
            name="createType"
            rules={[{ required: true, message: '请选择选品方式' }]}
            initialValue={FilterType.FreeSelect}
          >
            <Radio.Group onChange={onSelectType}>
              {goodsTypeValue !== 'SPU' ? (
                <>
                  <Radio value={FilterType.FreeSelect} style={{ margin: '4px 0 10px' }}>
                    按规则选品（通过物料基础属性、标签、人群属性、业务指标等规则圈选货品）
                  </Radio>
                  <Radio value={FilterType.Upload} style={{ marginBottom: '10px' }}>
                    手工上传（通过上传Excle、Odps表，生产线上品集，品集可用于投放或分析）
                  </Radio>
                  <Radio value={FilterType.Inventory} style={{ marginBottom: '10px' }}>
                    二次选品（在现有选品池基础上增加部分新选品规则，并生成新的品池）
                  </Radio>
                </>
              ) : (
                <Radio value={FilterType.Upload}>
                  手工上传（通过上传Excle、Odps表，生产线上品集，品集可用于投放或分析）
                </Radio>
              )}
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.goodsType !== currentValues.goodsType}
          >
            {({ getFieldValue }) =>
              getFieldValue('goodsType') === 'TRAVEL' ? (
                <Form.Item
                  label="是否打标"
                  name="hasTag"
                  rules={[{ required: true, message: '请选择选品方式' }]}
                  initialValue={false}
                  extra={<span style={{ color: '#999' }}>系统自动为品池内物料打ic标，品池过期自动去标</span>}
                >
                  <Radio.Group>
                    <Radio value={false}>否</Radio>
                    <Radio value={true}>是</Radio>
                  </Radio.Group>
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            label="数据更新频率"
            name="updateTime"
            rules={[{ required: true, message: '请选择数据更新频率' }]}
            initialValue={UpdateTimeTypeEnum.IMMUATE}
          >
            <Radio.Group>
              <Radio value={UpdateTimeTypeEnum.IMMUATE}>不更新</Radio>
              <Radio value={UpdateTimeTypeEnum.EVERYDAY}>每日下午三点更新</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="应用场景"
            name="applyScenes"
            rules={[{ required: true, message: '请选择应用场景' }]}
            initialValue={ApplyScenesEnum.NORMAL}
            extra={
              <span style={{ color: '#999' }}>
                无特殊场景选默认场景；快照匹配场景用于实时判定商品是否在池子中，rt在5ms内
              </span>
            }
          >
            <Radio.Group>
              {Object.keys(APPLY_SCENES_MAP).map((ele) => (
                <Radio value={ele}>{(APPLY_SCENES_MAP as any)[ele]}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>

          <Form.Item label="备注" name="subTitle">
            <TextArea placeholder="请输入备注" showCount maxLength={40} />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 11, span: 8 }}>
            <Button type="primary" htmlType="submit">
              下一步
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default StepOneCreateSelection;
