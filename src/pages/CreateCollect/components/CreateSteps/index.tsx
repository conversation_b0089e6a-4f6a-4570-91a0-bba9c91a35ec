import { Steps } from 'antd';
import { CollectStatusEnum } from '../../constants';

import './index.less';

const { Step } = Steps;

const CreateSteps = (props: { current: CollectStatusEnum; isShowTag: number }) => {
  const { current, isShowTag } = props;
  return (
    <div className="steps-container">
      <Steps size="small" current={current}>
        <Step title="填写信息" />
        <Step title="圈选货品" />
        {isShowTag === 1 && <Step title="货品打标" />}
        <Step title="发布品集" />
        <Step title="货品完成选品" />
      </Steps>
    </div>
  );
};

export default CreateSteps;
